import logging
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
from scipy import stats

from config import Configs
from config.logger import Logger


class DataStatistic:
    """
    数据统计分析类

    功能：
    1. 读取钢卷跟踪历史数据
    2. 根据时间段计算各种统计指标
    3. 应用字段映射
    4. 保存统计结果
    """

    def __init__(
        self,
        data_dir: str = "./data",
        head_offset_minutes: float = 0.0,
        tail_offset_minutes: float = 0.0,
    ):
        """
        初始化数据统计类

        Args:
            data_dir: 数据目录路径
            head_offset_minutes: 头部焊缝出炉时间偏移量（分钟），正值表示延后
            tail_offset_minutes: 尾部焊缝入炉时间偏移量（分钟），正值表示提前
        """
        self.data_dir = Path(data_dir)
        self.head_offset_minutes = head_offset_minutes
        self.tail_offset_minutes = tail_offset_minutes
        self.logger = logging.getLogger(self.__class__.__name__)

        # 初始化配置
        try:
            self.config = Configs.get_train_config()
        except Exception as e:
            self.logger.warning(f"配置初始化失败，使用默认配置: {e}")
            from config.configs import TrainConfig

            self.config = TrainConfig()

        # 输入输出文件路径
        self.input_file = self.data_dir / "data_track_history.csv"
        self.output_file = self.data_dir / "data_track_statistic.csv"

        self.logger.info(
            f"数据统计初始化完成 - 输入: {self.input_file}, 输出: {self.output_file}"
        )
        self.logger.info(
            f"时间偏移设置 - 头部偏移: {head_offset_minutes}分钟, 尾部偏移: {tail_offset_minutes}分钟"
        )

    def load_track_history(self) -> pd.DataFrame:
        """
        加载钢卷跟踪历史数据

        Returns:
            DataFrame: 钢卷跟踪历史数据
        """
        try:
            if not self.input_file.exists():
                raise FileNotFoundError(f"输入文件不存在: {self.input_file}")

            df = pd.read_csv(self.input_file)
            self.logger.info(f"成功加载钢卷跟踪数据，共 {len(df)} 条记录")

            # 转换时间列为datetime类型
            time_columns = [
                "loading_start_time",
                "loading_end_time",
                "head_weld_in_time",
                "head_weld_out_time",
                "tail_weld_in_time",
                "tail_weld_out_time",
            ]

            for col in time_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors="coerce", format="mixed")

            return df
        except Exception as e:
            self.logger.error(f"加载钢卷跟踪数据失败: {e}")
            raise

    def calculate_time_range(
        self, row: pd.Series
    ) -> tuple[Optional[datetime], Optional[datetime]]:
        """
        计算统计时间段范围

        Args:
            row: 钢卷数据行

        Returns:
            tuple: (开始时间, 结束时间)
        """
        try:
            head_weld_out_time = row["head_weld_out_time"]
            tail_weld_in_time = row["tail_weld_in_time"]

            # 检查时间是否有效
            if pd.isna(head_weld_out_time) or pd.isna(tail_weld_in_time):
                self.logger.warning(f"钢卷 {row.get('name', 'Unknown')} 的时间数据无效")
                return None, None

            # 应用时间偏移
            start_time = head_weld_out_time + timedelta(
                minutes=self.head_offset_minutes
            )
            end_time = tail_weld_in_time - timedelta(minutes=self.tail_offset_minutes)

            # 检查时间范围是否合理
            if start_time >= end_time:
                self.logger.warning(
                    f"钢卷 {row.get('name', 'Unknown')} 的时间范围无效: {start_time} >= {end_time}"
                )
                return None, None

            return start_time, end_time

        except Exception as e:
            self.logger.error(f"计算时间范围失败: {e}")
            return None, None

    def load_time_series_data(
        self, start_time: datetime, end_time: datetime
    ) -> Optional[pd.DataFrame]:
        """
        从InfluxDB加载指定时间段的时间序列数据

        Args:
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            DataFrame: 时间序列数据，如果没有数据则返回None
        """
        try:
            self.logger.info(f"从InfluxDB加载时间序列数据: {start_time} - {end_time}")
            return self._load_from_influxdb(start_time, end_time)

        except Exception as e:
            self.logger.error(f"加载时间序列数据失败: {e}")
            return None

    def _load_from_influxdb(
        self, start_time: datetime, end_time: datetime
    ) -> Optional[pd.DataFrame]:
        """
        从InfluxDB加载时间序列数据

        Args:
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            DataFrame: 时间序列数据
        """
        try:
            from utils.influxql_helper import InfluxQuery

            # 获取InfluxDB配置
            influxdb_config = Configs.get_influx_db_config()

            # 创建InfluxDB连接
            influx_query = InfluxQuery(
                host=influxdb_config.host,
                port=influxdb_config.port,
                username=influxdb_config.username,
                password=influxdb_config.password,
                database=influxdb_config.database,
            )

            # 获取所有测量点名称
            measurements = list(self.config.statistic_columns.keys())
            self.logger.info(f"准备从InfluxDB查询 {len(measurements)} 个测量点")

            # 使用batch_query_aligned_df进行批量查询
            df = influx_query.batch_query_aligned_df(
                measurements=measurements,
                field="value",
                start_time=start_time,
                end_time=end_time,
            )

            # 关闭数据库连接
            influx_query.close()

            if df is None or df.empty:
                self.logger.warning("从InfluxDB查询结果为空")
                return None

            # 确保返回的是DataFrame类型
            if not isinstance(df, pd.DataFrame):
                self.logger.error(f"查询结果类型错误: {type(df)}")
                return None

            # 重命名列
            if self.config.measurements:
                try:
                    df = df.rename(columns=self.config.statistic_columns)
                except Exception as e:
                    self.logger.warning(f"重命名列失败: {e}")

            self.logger.info(f"从InfluxDB成功查询到数据，形状: {df.shape}")
            return df

        except Exception as e:
            self.logger.error(f"从InfluxDB加载数据失败: {e}")
            return None

    def calculate_statistics(
        self, data: pd.DataFrame, columns: List[str]
    ) -> Dict[str, Dict[str, float]]:
        """
        计算统计指标

        Args:
            data: 时间序列数据
            columns: 需要计算统计的列名列表

        Returns:
            Dict: 统计结果，格式为 {column: {stat_name: value}}
        """
        try:
            statistics = {}

            for col in columns:
                if col not in data.columns:
                    self.logger.warning(f"列 {col} 不存在于数据中")
                    continue

                series = data[col].dropna()
                if len(series) == 0:
                    self.logger.warning(f"列 {col} 没有有效数据")
                    continue

                col_stats = {}

                # 基本统计指标
                col_stats["max"] = float(series.max())
                col_stats["min"] = float(series.min())
                col_stats["mean"] = float(series.mean())
                col_stats["std"] = float(series.std())
                col_stats["peak_to_peak"] = col_stats["max"] - col_stats["min"]

                # 高级统计指标
                try:
                    col_stats["skewness"] = float(stats.skew(series))
                    col_stats["kurtosis"] = float(stats.kurtosis(series))
                except Exception as e:
                    self.logger.warning(f"计算列 {col} 的高级统计指标失败: {e}")
                    col_stats["skewness"] = np.nan
                    col_stats["kurtosis"] = np.nan

                statistics[col] = col_stats

            return statistics

        except Exception as e:
            self.logger.error(f"计算统计指标失败: {e}")
            return {}

    def apply_field_mapping(
        self, statistics: Dict[str, Dict[str, float]]
    ) -> Dict[str, Dict[str, float]]:
        """
        应用字段映射

        Args:
            statistics: 原始统计结果

        Returns:
            Dict: 映射后的统计结果
        """
        try:
            mapped_statistics = {}

            # 获取字段映射关系（从原始字段名到映射后字段名）
            field_mapping = {v: k for k, v in self.config.measurements.items()}

            for original_field, stats_dict in statistics.items():
                # 如果字段在映射中，使用映射后的名称，否则保持原名称
                mapped_field = field_mapping.get(original_field, original_field)
                mapped_statistics[mapped_field] = stats_dict

            return mapped_statistics

        except Exception as e:
            self.logger.error(f"应用字段映射失败: {e}")
            return statistics

    def format_statistics_for_output(
        self, strip_info: pd.Series, statistics: Dict[str, Dict[str, float]]
    ) -> Dict[str, Any]:
        """
        格式化统计结果用于输出

        Args:
            strip_info: 钢卷信息
            statistics: 统计结果

        Returns:
            Dict: 格式化后的输出数据
        """
        try:
            output_row = {}

            # 添加钢卷基本信息
            output_row["strip_name"] = strip_info.get("name", "")
            output_row["strip_type"] = strip_info.get("type", "")
            output_row["width"] = strip_info.get("width", np.nan)
            output_row["thick"] = strip_info.get("thick", np.nan)
            output_row["length"] = strip_info.get("length", np.nan)
            output_row["weight"] = strip_info.get("weight", np.nan)

            # 添加时间信息
            start_time, end_time = self.calculate_time_range(strip_info)
            output_row["stat_start_time"] = (
                start_time.isoformat() if start_time else None
            )
            output_row["stat_end_time"] = end_time.isoformat() if end_time else None

            # 添加统计指标
            for field, stats_dict in statistics.items():
                for stat_name, stat_value in stats_dict.items():
                    column_name = f"{field}_{stat_name}"
                    output_row[column_name] = stat_value

            return output_row

        except Exception as e:
            self.logger.error(f"格式化统计结果失败: {e}")
            return {}

    def process_single_strip(self, strip_info: pd.Series) -> Optional[Dict[str, Any]]:
        """
        处理单个钢卷的统计计算

        Args:
            strip_info: 钢卷信息

        Returns:
            Dict: 统计结果，如果处理失败则返回None
        """
        try:
            strip_name = strip_info.get("name", "Unknown")
            self.logger.info(f"开始处理钢卷: {strip_name}")

            # 计算时间范围
            start_time, end_time = self.calculate_time_range(strip_info)
            if start_time is None or end_time is None:
                self.logger.warning(f"钢卷 {strip_name} 时间范围无效，跳过处理")
                return None

            # 加载时间序列数据
            time_series_data = self.load_time_series_data(start_time, end_time)
            if time_series_data is None or time_series_data.empty:
                self.logger.warning(
                    f"钢卷 {strip_name} 没有可用的时间序列数据，跳过处理"
                )
                return None

            # 获取需要统计的列（使用映射后的列名）
            stat_columns = list(self.config.measurements.values())
            available_columns = [
                col for col in stat_columns if col in time_series_data.columns
            ]

            if not available_columns:
                self.logger.warning(f"钢卷 {strip_name} 没有可统计的列，跳过处理")
                return None

            # 计算统计指标
            statistics = self.calculate_statistics(time_series_data, available_columns)
            if not statistics:
                self.logger.warning(f"钢卷 {strip_name} 统计计算失败，跳过处理")
                return None

            # 应用字段映射
            mapped_statistics = self.apply_field_mapping(statistics)

            # 格式化输出
            output_row = self.format_statistics_for_output(
                strip_info, mapped_statistics
            )

            self.logger.info(
                f"钢卷 {strip_name} 处理完成，计算了 {len(statistics)} 个字段的统计指标"
            )
            return output_row

        except Exception as e:
            self.logger.error(f"处理钢卷失败: {e}")
            return None

    def run_statistics(self) -> bool:
        """
        运行统计分析

        Returns:
            bool: 是否成功完成
        """
        try:
            self.logger.info("开始数据统计分析")

            # 加载钢卷跟踪历史数据
            track_history = self.load_track_history()
            if track_history.empty:
                self.logger.error("没有可用的钢卷跟踪数据")
                return False

            # 处理每个钢卷
            results = []
            total_strips = len(track_history)
            processed_count = 0

            for i, (_, strip_info) in enumerate(track_history.iterrows()):
                result = self.process_single_strip(strip_info)
                if result is not None:
                    results.append(result)
                    processed_count += 1

                if (i + 1) % 10 == 0:
                    self.logger.info(f"已处理 {i + 1}/{total_strips} 个钢卷")

            if not results:
                self.logger.error("没有成功处理的钢卷数据")
                return False

            # 保存结果
            results_df = pd.DataFrame(results)
            self.save_results(results_df)

            self.logger.info(
                f"统计分析完成，共处理 {processed_count}/{total_strips} 个钢卷"
            )
            return True

        except Exception as e:
            self.logger.error(f"统计分析失败: {e}")
            return False

    def save_results(self, results_df: pd.DataFrame) -> None:
        """
        保存统计结果

        Args:
            results_df: 统计结果DataFrame
        """
        try:
            # 确保输出目录存在
            self.output_file.parent.mkdir(parents=True, exist_ok=True)

            # 保存到CSV文件
            results_df.to_csv(self.output_file, index=False)
            self.logger.info(f"统计结果已保存到: {self.output_file}")

            # 打印基本信息
            self.logger.info(
                f"保存了 {len(results_df)} 条记录，{len(results_df.columns)} 个字段"
            )

        except Exception as e:
            self.logger.error(f"保存统计结果失败: {e}")
            raise


def main():
    """
    主函数，用于测试和运行统计分析
    """
    try:
        # 创建统计分析实例
        # 可以调整时间偏移参数来缩短统计时间段
        statistic = DataStatistic(
            data_dir="./data",
            head_offset_minutes=0,  # 头部焊缝出炉后5分钟开始统计
            tail_offset_minutes=0,  # 尾部焊缝入炉前5分钟结束统计
        )

        # 运行统计分析
        success = statistic.run_statistics()

        if success:
            logging.info("数据统计分析成功完成")
        else:
            logging.error("数据统计分析失败")

    except Exception as e:
        logging.error(f"程序运行失败: {e}")


if __name__ == "__main__":
    Configs.initialize()
    Logger.initialize()
    main()
