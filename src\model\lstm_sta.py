import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import lightning as L
from torch.optim.lr_scheduler import ReduceLROnPlateau
from typing import Dict, List, Tuple, Optional, Union, Any
import math


class SpatialAttention(nn.Module):
    def __init__(self, input_dim):
        super().__init__()
        self.query = nn.Linear(input_dim, input_dim)
        self.key = nn.Linear(input_dim, input_dim)

    def forward(self, x):
        # x: [B, T, D]
        Q = self.query(x)  # [B, T, D]
        K = self.key(x)  # [B, T, D]
        attn_scores = torch.matmul(Q, K.transpose(1, 2))  # [B, T, T]
        attn_weights = F.softmax(attn_scores, dim=-1)
        return torch.matmul(attn_weights, x)  # [B, T, D]


class TemporalAttention(nn.Module):
    def __init__(self, hidden_size):
        super().__init__()
        self.W = nn.Linear(hidden_size, hidden_size)
        self.v = nn.Linear(hidden_size, 1, bias=False)

    def forward(self, hidden_states):
        # hidden_states: [B, T, H]
        energy = torch.tanh(self.W(hidden_states))  # [B, T, H]
        scores = self.v(energy).squeeze(-1)  # [B, T]
        weights = F.softmax(scores, dim=1)
        return torch.sum(hidden_states * weights.unsqueeze(-1), dim=1)  # [B, H]


class LSTMSTAModel(nn.Module):
    def __init__(
        self,
        input_size: int,
        lstm_input_size: int,
        lstm_hidden_size: int,
        output_size: int,
        sequence_length: int,
        dropout: float = 1e-3,
    ):
        super().__init__()

        # 参数验证
        assert (
            input_size == sequence_length * lstm_input_size
        ), "input_size must be sequence_length * lstm_input_size"

        # 保存参数
        self.sequence_length = sequence_length
        self.lstm_input_size = lstm_input_size

        # 网络结构
        self.batch_norm = nn.BatchNorm1d(input_size)
        self.input_fc = nn.Linear(input_size, input_size)
        self.dropout = nn.Dropout(dropout)
        self.spatial_attn = SpatialAttention(lstm_input_size)
        self.lstm = nn.LSTM(lstm_input_size, lstm_hidden_size, batch_first=True)
        self.temporal_attn = TemporalAttention(lstm_hidden_size)
        self.output_fc = nn.Linear(lstm_hidden_size, output_size)

    def forward(self, x):
        # x: [B, input_size]
        x = self.batch_norm(x)
        x = self.input_fc(x)
        x = self.dropout(x)

        # 重塑为序列 [B, T, D]
        x = x.view(-1, self.sequence_length, self.lstm_input_size)

        # 空间注意力
        x = self.spatial_attn(x)

        # LSTM处理
        lstm_out, _ = self.lstm(x)  # [B, T, H]

        # 时间注意力
        attn_out = self.temporal_attn(lstm_out)

        # 输出层
        return self.output_fc(attn_out)

    def get_attention_weights(self, x):
        """
        获取注意力权重，用于可视化分析

        Args:
            x: 输入张量 [B, input_size]

        Returns:
            spatial_weights: 空间注意力权重 [B, T, T]
            temporal_weights: 时间注意力权重 [B, T]
        """
        # 预处理
        x = self.batch_norm(x)
        x = self.input_fc(x)
        x = self.dropout(x)

        # 重塑为序列
        x = x.view(-1, self.sequence_length, self.lstm_input_size)

        # 获取空间注意力权重
        Q = self.spatial_attn.query(x)
        K = self.spatial_attn.key(x)
        spatial_attn_scores = torch.matmul(Q, K.transpose(1, 2))
        spatial_weights = F.softmax(spatial_attn_scores, dim=-1)

        # 应用空间注意力
        x = torch.matmul(spatial_weights, x)

        # LSTM处理
        lstm_out, _ = self.lstm(x)

        # 获取时间注意力权重
        energy = torch.tanh(self.temporal_attn.W(lstm_out))
        scores = self.temporal_attn.v(energy).squeeze(-1)
        temporal_weights = F.softmax(scores, dim=1)

        return spatial_weights, temporal_weights


class LitLSTMSTA(L.LightningModule):
    """
    PyTorch Lightning LSTM-STA模型

    这是对LSTMSTAModel的Lightning封装，提供了完整的训练、验证和测试流程。
    包含空间-时间注意力机制的LSTM模型，适用于序列预测任务。
    """

    def __init__(
        self,
        input_size: int,
        lstm_input_size: int,
        lstm_hidden_size: int,
        output_size: int,
        sequence_length: int,
        dropout: float = 0.1,
        learning_rate: float = 1e-3,
        weight_decay: float = 1e-5,
    ):
        """
        初始化Lightning LSTM-STA模型

        Args:
            input_size: 输入特征的总维度
            lstm_input_size: LSTM输入特征的维度
            lstm_hidden_size: LSTM隐藏层的维度
            output_size: 输出特征的维度
            sequence_length: 序列长度
            dropout: dropout比率，默认为0.1
            learning_rate: 学习率，默认为1e-3
            weight_decay: 权重衰减系数，默认为1e-5
        """
        super().__init__()
        self.save_hyperparameters()

        # 保存超参数
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay

        # 创建LSTM-STA模型
        self.model = LSTMSTAModel(
            input_size=input_size,
            lstm_input_size=lstm_input_size,
            lstm_hidden_size=lstm_hidden_size,
            output_size=output_size,
            sequence_length=sequence_length,
            dropout=dropout,
        )

        # 损失函数
        self.criterion = nn.MSELoss()

        # 用于跟踪最佳验证损失
        self.best_val_loss = float("inf")

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量，形状为 [batch_size, input_size]

        Returns:
            输出张量，形状为 [batch_size, output_size]
        """
        return self.model(x)

    def training_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        训练步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含损失的字典
        """
        x, y = batch

        # 形状转换：将3D张量[batch_size, input_len, num_features]展平为2D张量[batch_size, input_size]
        if len(x.shape) == 3:
            x = x.view(x.size(0), -1)

        # 前向传播
        y_hat = self(x)

        # 处理多维目标值
        if len(y.shape) == 3:
            # 多步预测：只取最后一个时间步
            y = y[:, -1, :]
        elif len(y.shape) == 2 and y.shape[1] > 1:
            # 多特征预测：保持原样
            pass

        # 计算损失
        loss = self.criterion(y_hat, y)

        # 记录训练损失
        self.log("train_loss", loss, prog_bar=True, on_step=True, on_epoch=True)

        return {"loss": loss}

    def validation_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        验证步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含验证损失和指标的字典
        """
        x, y = batch

        # 形状转换：将3D张量[batch_size, input_len, num_features]展平为2D张量[batch_size, input_size]
        if len(x.shape) == 3:
            x = x.view(x.size(0), -1)

        y_hat = self(x)

        # 处理多维目标值
        if len(y.shape) == 3:
            y = y[:, -1, :]
        elif len(y.shape) == 2 and y.shape[1] > 1:
            pass

        # 计算损失和指标
        val_loss = self.criterion(y_hat, y)
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(val_loss)

        # 更新最佳验证损失
        if val_loss < self.best_val_loss:
            self.best_val_loss = val_loss

        # 记录指标
        self.log("val_loss", val_loss, prog_bar=True)
        self.log("val_mae", mae, prog_bar=True)
        self.log("val_rmse", rmse, prog_bar=True)
        self.log("best_val_loss", self.best_val_loss, prog_bar=False)

        return {"val_loss": val_loss, "val_mae": mae, "val_rmse": rmse}

    def test_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        测试步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含测试损失和指标的字典
        """
        x, y = batch

        # 形状转换：将3D张量[batch_size, input_len, num_features]展平为2D张量[batch_size, input_size]
        if len(x.shape) == 3:
            x = x.view(x.size(0), -1)

        y_hat = self(x)

        # 处理多维目标值
        if len(y.shape) == 3:
            y = y[:, -1, :]
        elif len(y.shape) == 2 and y.shape[1] > 1:
            pass

        # 计算损失和指标
        test_loss = self.criterion(y_hat, y)
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(test_loss)

        # 记录指标
        self.log("test_loss", test_loss)
        self.log("test_mae", mae)
        self.log("test_rmse", rmse)

        return {"test_loss": test_loss, "test_mae": mae, "test_rmse": rmse}

    def predict_step(self, batch: torch.Tensor, batch_idx: int) -> torch.Tensor:
        """
        预测步骤

        Args:
            batch: 输入张量或包含输入的元组
            batch_idx: 批次索引

        Returns:
            预测结果
        """
        if isinstance(batch, tuple):
            x = batch[0]
        else:
            x = batch

        # 形状转换：将3D张量[batch_size, input_len, num_features]展平为2D张量[batch_size, input_size]
        if len(x.shape) == 3:
            x = x.view(x.size(0), -1)

        return self(x)

    def configure_optimizers(self) -> Dict[str, Any]:
        """
        配置优化器和学习率调度器

        Returns:
            包含优化器和学习率调度器的字典
        """
        optimizer = torch.optim.Adam(
            self.parameters(), lr=self.learning_rate, weight_decay=self.weight_decay
        )

        scheduler = {
            "scheduler": ReduceLROnPlateau(
                optimizer,
                mode="min",
                factor=0.5,
                patience=5,
                min_lr=1e-6,
            ),
            "monitor": "val_loss",
            "interval": "epoch",
            "frequency": 1,
        }

        return {"optimizer": optimizer, "lr_scheduler": scheduler}

    def get_attention_weights(
        self, x: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        获取注意力权重，用于可视化分析

        Args:
            x: 输入张量 [batch_size, input_size]

        Returns:
            spatial_weights: 空间注意力权重 [batch_size, T, T]
            temporal_weights: 时间注意力权重 [batch_size, T]
        """
        return self.model.get_attention_weights(x)
